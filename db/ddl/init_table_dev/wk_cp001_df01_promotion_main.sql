CREATE TABLE wk_cp001_df01_promotion_main (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    promotion_id VARCHAR(26) NOT NULL,
    name VARCHAR(50) NOT NULL,
    promotion_type VARCHAR(2) NOT NULL,
    present_product_code VARCHAR(16),
    first_order_only VARCHAR(5) NOT NULL,
    discount_rate NUMERIC(3,0),
    oneshot_order_limit NUMERIC(8,0),
    campaign_quantity_limit NUMERIC(8,0),
    campaign_end_date VARCHAR(29) NOT NULL,
    preferential_product_flg VARCHAR(5) NOT NULL,
    baitai_code VARCHAR(10),
    campaign_priority NUMERIC(5,0),
    discount_amount NUMERIC(8,0),
    discount_retail_price NUMERIC(8,0),
    single_product_flg VARCHAR(1),
    discount_amount_amount VARCHAR(10) NOT NULL,
    discount_rate_amount VARCHAR(5) NOT NULL,
    pricebook_id VARCHAR(37) NOT NULL,
    split_num NUMERIC(2,0),
    PRIMARY KEY (campaign_instructions_code, promotion_id)
);
COMMENT ON TABLE wk_cp001_df01_promotion_main IS 'プロモーション出力ワーク';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.promotion_id IS 'プロモーション番号';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.name IS 'キャンペーン設定名称';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.promotion_type IS 'プロモーション種別';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.present_product_code IS 'プレゼント商品コード';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.first_order_only IS '初回注文限定フラグ';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.discount_rate IS '値引率';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.oneshot_order_limit IS '注文毎注文上限数';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.campaign_quantity_limit IS 'キャンペーン累積受注上限数';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.campaign_end_date IS 'キャンペーン適用終了日';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.preferential_product_flg IS '優待商品フラグ';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.baitai_code IS '媒体コード';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.campaign_priority IS 'キャンペーン優先順位';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.discount_amount IS '値引額';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.discount_retail_price IS '値引後販売価格';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.single_product_flg IS '単一商品フラグ';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.discount_amount_amount IS '値引額(NOT NULL)';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.discount_rate_amount IS '値引率(NOT NULL)';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.pricebook_id IS '価格表ID';
COMMENT ON COLUMN wk_cp001_df01_promotion_main.split_num IS '分割単位';

