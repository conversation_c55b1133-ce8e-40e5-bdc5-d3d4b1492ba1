CREATE TABLE wk_cp001_df01_static_customer_groups_campaign_main (
    group_id VARCHAR(25) NOT NULL,
    campaign_instructions_name VARCHAR(50) NOT NULL,
    split_num NUMERIC(2,0),
    PRIMARY KEY (group_id)
);
COMMENT ON TABLE wk_cp001_df01_static_customer_groups_campaign_main IS '静的顧客基本情報出力ワーク';
COMMENT ON COLUMN wk_cp001_df01_static_customer_groups_campaign_main.group_id IS '顧客グループID';
COMMENT ON COLUMN wk_cp001_df01_static_customer_groups_campaign_main.campaign_instructions_name IS 'キャンペーン設定名称';
COMMENT ON COLUMN wk_cp001_df01_static_customer_groups_campaign_main.split_num IS '分割単位';

