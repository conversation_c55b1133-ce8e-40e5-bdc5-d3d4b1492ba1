CREATE TABLE wk_cp001_df01_updated_campaign_promotion_list (
    campaign_instructions_code VARCHAR(16) NOT NULL,
    campaign_price_flg VARCHAR(1) NOT NULL,
    static_customer_flg VARCHAR(1) NOT NULL,
    dynamic_customer_flg VARCHAR(1) NOT NULL,
    PRIMARY KEY (campaign_instructions_code)
);
COMMENT ON TABLE wk_cp001_df01_updated_campaign_promotion_list IS 'プロモーション連携対象抽出ワーク';
COMMENT ON COLUMN wk_cp001_df01_updated_campaign_promotion_list.campaign_instructions_code IS 'キャンペーン設定コード';
COMMENT ON COLUMN wk_cp001_df01_updated_campaign_promotion_list.campaign_price_flg IS 'キャンペーン価格フラグ';
COMMENT ON COLUMN wk_cp001_df01_updated_campaign_promotion_list.static_customer_flg IS '静的顧客フラグ';
COMMENT ON COLUMN wk_cp001_df01_updated_campaign_promotion_list.dynamic_customer_flg IS '動的顧客フラグ';

